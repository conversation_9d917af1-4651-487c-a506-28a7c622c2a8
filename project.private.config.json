{"description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectname": "xxxx", "setting": {"compileHotReLoad": true, "urlCheck": false, "bigPackageSizeSupport": true, "coverView": false, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": false, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true}, "condition": {"miniprogram": {"list": [{"name": "高舱购-入口", "pathName": "subpackagesHt/pages/entry/index", "query": "", "scene": null}, {"name": "销售页-次卡员工信息填写", "pathName": "subpackagesHt/pages/verification/employee-card-verification/index", "query": "jobNumber=ht_test1", "scene": null}, {"name": "销售页-高舱旅客信息填写", "pathName": "subpackagesHt/pages/product/lounge-list/passenger-info/index", "query": "productId=1928045447014363136&productType=2", "scene": null}, {"name": "销售页-产品确认", "pathName": "subpackagesHt/pages/product/employee-card-list/confirm/index", "query": "productId=1925067907181879296&productType=2&passengerName=HUYAXUAN&passengerPhone=15114084632&flightNumber=CA4392&flightDate=2025-04-01&gateNumber=012&seatNumber=024L", "scene": null}, {"name": "支付页-产品支付", "pathName": "subpackagesHt/pages/payment/index", "query": "orderNo=202505221017001", "scene": null}, {"name": "支付页-结果", "pathName": "subpackagesHt/pages/payment/result/index", "query": "orderNo=202505141012001", "scene": null}, {"name": "核销页", "pathName": "subpackagesHt/pages/verification/index", "query": "", "scene": null}, {"name": "核销页-员工次卡", "pathName": "subpackagesHt/pages/verification/employee-card-verification/index", "query": "jobNumber=LS0030", "scene": null}, {"name": "核销页-旅客", "pathName": "subpackagesHt/pages/verification/code-input/index", "query": "", "scene": null}, {"name": "核销页-核销订单选择", "pathName": "subpackagesHt/pages/verification/code-input/order-select/index", "query": "codeNo=622545", "scene": null}, {"name": "核销页-核销确认", "pathName": "subpackagesHt/pages/verification/code-input/confirm/index", "query": "productCodeId=1926955520539250688", "scene": null}, {"name": "核销页-核销成功", "pathName": "subpackagesHt/pages/verification/code-input/success/index", "query": "productCodeId=1925363909774606336", "scene": null}, {"name": "订单页-我的销售订单", "pathName": "subpackagesHt/pages/order/index", "query": "", "scene": null}, {"name": "订单页-全部销售订单", "pathName": "subpackagesHt/pages/order/index", "query": "type=all", "scene": null}, {"name": "订单页-订单详情", "pathName": "subpackagesHt/pages/order/detail/index", "query": "orderNo=202505291926001", "scene": null}, {"name": "我的-优惠券", "pathName": "subpackagesHt/pages/me/coupon/index", "query": "", "scene": null}, {"name": "我的-产品使用记录", "pathName": "subpackagesHt/pages/me/record/index", "query": "", "scene": null}, {"name": "我的-补全信息", "pathName": "subpackagesHt/pages/me/complete-info/index", "query": "", "scene": null}, {"name": "我的-个人信息", "pathName": "subpackagesHt/pages/me/profile/index", "query": "", "scene": null}, {"name": "我的-修改密码", "pathName": "subpackagesHt/pages/me/change-password/index", "query": "", "scene": null}]}}, "libVersion": "development"}