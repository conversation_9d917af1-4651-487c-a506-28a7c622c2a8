import { useEffect } from 'react';
import { showToast } from '@tarojs/taro';
import request from '@ht/api/apiConfig';
import {
  useUserStore,
  EmployeeInfo,
  isValidEmployeeInfo,
} from '@ht/store/userStore';
import { AES } from '../AES';

// 用于跟踪全局初始化状态
let isGlobalInitialized = false;

/**
 * 通过API获取指定用户ID的用户信息
 * @param userId 用户ID
 * @returns 成功返回用户信息，失败返回null
 */
export const fetchUserInfoById = async (
  userId: string,
): Promise<EmployeeInfo | null> => {
  try {
    // 显示加载提示
    showToast({
      title: '获取用户信息中...',
      icon: 'loading',
      duration: 10 * 1e3,
    });

    // 调用API获取用户信息
    const res = await request({
      path: `/uc/api/admin/user/get/${userId}`,
      method: 'GET',
      isloading: false, // 我们自己管理loading状态
    });

    // console.log('获取用户信息成功:', res);

    // 检查API返回是否成功
    if (res && res.data) {
      // 构建员工信息对象
      const employeeInfo: EmployeeInfo = {
        name: res.data.employeeName || '',
        phone: res.data.phone ? AES.decrypt(res.data.phone) : '',
        jobNumber: res.data.jobNumber || '',
      };

      // 验证信息是否完整
      if (isValidEmployeeInfo(employeeInfo)) {
        showToast({
          title: '已获取员工信息',
          icon: 'success',
        });

        return employeeInfo;
      } else {
        showToast({
          title: '获取的用户信息不完整',
          icon: 'none',
        });
      }
    } else {
      showToast({
        title: '获取用户信息失败',
        icon: 'none',
      });
    }
  } catch (error) {
    // console.error('获取用户信息出错:', error);
    showToast({
      title: '获取用户信息失败',
      icon: 'none',
    });
  }

  return null;
};

/**
 * 用户信息自定义Hook
 * 使用zustand管理用户数据，替代原来的本地存储方式
 */
export const useUserInfo = () => {
  // 从zustand store中获取状态和方法
  const { userInfo, userData, loading, fetchUserInfo } = useUserStore();

  // 初始化时获取用户信息，只执行一次
  useEffect(() => {
    // 如果全局已初始化，则不再重复请求
    if (!isGlobalInitialized) {
      isGlobalInitialized = true;

      // 调用fetchUserInfo
      const fetchData = async () => {
        try {
          await fetchUserInfo();
          // 如果fetchUserInfo内部有错误但没有抛出，我们可以通过检查store状态来判断是否成功
          const { initialized } = useUserStore.getState();
          if (!initialized) {
            // 如果初始化失败，将isGlobalInitialized设置为false
            isGlobalInitialized = false;
            // console.error('获取用户信息失败: 初始化状态为false');
          }
        } catch (error) {
          // console.error('获取用户信息失败:', error);
          // 当fetchUserInfo失败时，将isGlobalInitialized设置为false
          isGlobalInitialized = false;
        }
      };

      fetchData();
    }
  }, []);

  return {
    userInfo,
    userData, // 返回完整的用户数据
    loading,
    refreshUserInfo: () => fetchUserInfo(true), // 提供刷新方法，强制从接口获取
  };
};
