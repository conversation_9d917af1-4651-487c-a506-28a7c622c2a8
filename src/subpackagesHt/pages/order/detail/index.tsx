import { View, Text, Button, ScrollView, Textarea } from '@tarojs/components';
import {
  useLoad,
  useRouter,
  showToast,
  navigateBack,
  navigateTo,
  showModal,
} from '@tarojs/taro';
import { useState } from 'react';
import dayjs from 'dayjs';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import {
  getStatusText,
  getStatusColorClass,
  getOrderTypeText,
  formatDateTime,
} from '@ht/utils/orderUtils';
import DetailCard from './components/DetailCard';
import DetailRow from './components/DetailRow';
import './index.less';

// 原始退款信息接口 - 与 swagger.json 中的 RefundInfoVo 保持一致
interface RefundInfo {
  applyReason?: string; // 申请原因
  applyTime?: string; // 申请时间
  applyUser?: string; // 申请人
  auditResult?: string; // 审核结果
  auditTime?: string; // 审批时间
  auditUser?: string; // 审核人
  offlineRefundTime?: string; // 下线退款时间
  offlineRefundUser?: string; // 下线退款人
  offlineRefundUserNo?: string; // 下线退款人工号
  refundOrderNo?: string; // 退款单号
  refundStatus?: string; // 退款状态
  refundSuccessTime?: string; // 退款到账时间
}

// 统一的退款信息项接口
interface RefundInfoItem {
  type: 'apply' | 'audit' | 'payment' | 'offline';
  status: string;
  data: Record<string, string>;
}

// 产品使用信息接口
interface ProductUseInfoVo {
  boardGate?: string; // 登机口
  codeNo?: string; // 券码
  fltDate?: string; // 航班日期
  fltNo?: string; // 航班号
  passengerName?: string; // 旅客姓名
  phone?: string; // 手机号
  productName?: string; // 产品名称
  seatNo?: string; // 座位号
  userNo?: string; // 工号
  verifyTime?: string; // 核销时间
  verifyUserName?: string; // 核销人姓名
  verifyUserNo?: string; // 核销人工号
}

// 订单详情接口
interface OrderDetailVO {
  orderNo?: string; // 订单号
  productId?: number; // 产品ID
  productName?: string; // 产品名称
  productType?: number; // 产品类型，1：员工次卡；2：旅客高舱
  price?: string; // 价格
  status?: number; // 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用
  createdName?: string; // 创建人
  createdNo?: string; // 创建工号
  createdTime?: string; // 创建时间
  payOrderNo?: string; // 支付交易号
  passengerName?: string; // 旅客姓名
  phone?: string; // 联系电话
  codeNoList?: string[]; // 券码列表
  fltNo?: string; // 航班号
  fltDate?: string; // 航班日期
  boardGate?: string; // 登机口
  seatNo?: string; // 座位号
  effectTime?: string; // 产品使用时间
  payTime?: string; // 支付时间
  cancelTime?: string; // 取消时间
  cancelType?: number; // 取消类型，1：手动取消；2：自动取消
  refundInfoList?: RefundInfo[]; // 退款信息列表
  productUseInfoList?: ProductUseInfoVo[]; // 产品使用信息列表
  times?: number; // 总次数
  useTimes?: string; // 已使用次数
  userNo?: string; // 购买人工号
}

export default function OrderDetail() {
  const router = useRouter();
  const { orderNo } = router.params;

  // 订单详情
  const [orderDetail, setOrderDetail] = useState<OrderDetailVO>({});

  // 加载状态
  const [loading, setLoading] = useState(true);

  // 退款模态框状态
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [refundReason, setRefundReason] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // 取消订单模态框状态
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelSubmitting, setCancelSubmitting] = useState(false);

  // 退款状态映射
  const getRefundStatusInfo = (type: string, item: RefundInfo) => {
    const statusMap = {
      apply: { status: '1', title: '申请退款' },
      audit: {
        status:
          item.auditResult === '1'
            ? '2-1'
            : item.auditResult === '2'
              ? '2-2'
              : '2',
        title:
          item.auditResult === '1'
            ? '审核通过'
            : item.auditResult === '2'
              ? '审核不通过'
              : '待审核',
      },
      payment: {
        status:
          item.refundStatus === '2'
            ? '3-2'
            : item.refundStatus === '3'
              ? '3-3'
              : item.refundStatus === '4'
                ? '3-4'
                : '3',
        title:
          item.refundStatus === '2'
            ? '退款中'
            : item.refundStatus === '3'
              ? '已退款'
              : item.refundStatus === '4'
                ? '退款失败'
                : '退款信息',
      },
      offline: { status: '4', title: '线下退款' },
    };
    return statusMap[type] || { status: '1', title: '申请退款' };
  };

  // 处理退款信息列表，将每个对象拆分成四种不同类型
  const processRefundInfoList = (refundInfoList?: RefundInfo[]) => {
    if (!refundInfoList || refundInfoList.length === 0) {
      return [];
    }

    // 先根据 applyTime 时间先后排序
    const sortedList = [...refundInfoList].sort((a, b) => {
      if (!a.applyTime) return 1;
      if (!b.applyTime) return -1;
      return dayjs(a.applyTime).valueOf() - dayjs(b.applyTime).valueOf();
    });

    const result: RefundInfoItem[] = [];

    sortedList.forEach(item => {
      // 申请退款信息
      if (item.applyUser && item.applyTime) {
        const statusInfo = getRefundStatusInfo('apply', item);
        result.push({
          type: 'apply',
          status: statusInfo.status,
          data: {
            applyUser: item.applyUser || '',
            applyTime: item.applyTime || '',
            applyReason: item.applyReason || '',
            title: statusInfo.title,
          },
        });
      }

      // 审批结果信息
      if (item.auditUser && item.auditTime) {
        const statusInfo = getRefundStatusInfo('audit', item);
        result.push({
          type: 'audit',
          status: statusInfo.status,
          data: {
            auditUser: item.auditUser || '',
            auditTime: item.auditTime || '',
            auditResult: item.auditResult || '',
            title: statusInfo.title,
          },
        });
      }

      // 退款信息
      if (item.refundOrderNo || item.refundSuccessTime) {
        const statusInfo = getRefundStatusInfo('payment', item);
        result.push({
          type: 'payment',
          status: statusInfo.status,
          data: {
            refundOrderNo: item.refundOrderNo || '',
            refundStatus: item.refundStatus || '',
            refundSuccessTime: item.refundSuccessTime || '',
            title: statusInfo.title,
          },
        });
      }

      // 线下退款信息
      if (item.offlineRefundUser && item.offlineRefundTime) {
        const statusInfo = getRefundStatusInfo('offline', item);
        result.push({
          type: 'offline',
          status: statusInfo.status,
          data: {
            offlineRefundUser: item.offlineRefundUser || '',
            offlineRefundTime: item.offlineRefundTime || '',
            offlineRefundUserNo: item.offlineRefundUserNo || '',
            title: statusInfo.title,
          },
        });
      }
    });

    return result;
  };

  // 渲染退款信息项的详细内容
  const renderRefundItemDetails = (refundInfo: RefundInfoItem) => {
    const { type, data } = refundInfo;

    switch (type) {
      case 'apply':
        return (
          <>
            <DetailRow
              label="申请人"
              value={data.applyUser}
              className="refund-detail-row"
            />
            <DetailRow
              label="申请时间"
              value={formatDateTime(data.applyTime)}
              className="refund-detail-row"
            />
            <DetailRow
              label="申请原因"
              value={data.applyReason}
              className="refund-detail-row"
            />
          </>
        );

      case 'audit': {
        const auditResultText =
          data.auditResult === '0'
            ? '待审核'
            : data.auditResult === '1'
              ? '审核通过'
              : data.auditResult === '2'
                ? '审核不通过'
                : data.auditResult;
        return (
          <>
            <DetailRow
              label="审批人"
              value={data.auditUser}
              className="refund-detail-row"
            />
            <DetailRow
              label="审批时间"
              value={formatDateTime(data.auditTime)}
              className="refund-detail-row"
            />
            <DetailRow
              label="审批结果"
              value={auditResultText}
              className="refund-detail-row"
            />
          </>
        );
      }

      case 'payment': {
        const refundStatusText =
          data.refundStatus === '1'
            ? '无'
            : data.refundStatus === '2'
              ? '退款中'
              : data.refundStatus === '3'
                ? '已退款'
                : data.refundStatus === '4'
                  ? '退款失败'
                  : data.refundStatus;
        return (
          <>
            <DetailRow
              label="退款交易号"
              value={data.refundOrderNo}
              className="refund-detail-row"
            />
            <DetailRow
              label="退款反馈"
              value={refundStatusText}
              className="refund-detail-row"
            />
            <DetailRow
              label="退款到账时间"
              value={formatDateTime(data.refundSuccessTime)}
              className="refund-detail-row"
            />
          </>
        );
      }

      case 'offline':
        return (
          <>
            <DetailRow
              label="线下退款人"
              value={data.offlineRefundUser}
              className="refund-detail-row"
            />
            <DetailRow
              label="线下退款时间"
              value={formatDateTime(data.offlineRefundTime)}
              className="refund-detail-row"
            />
            {data.offlineRefundUserNo && (
              <DetailRow
                label="退款人工号"
                value={data.offlineRefundUserNo}
                className="refund-detail-row"
              />
            )}
          </>
        );

      default:
        return null;
    }
  };

  // 获取订单详情
  const fetchOrderDetail = async () => {
    if (!orderNo) {
      showToast({
        title: '订单号不存在',
        icon: 'none',
        duration: 2000,
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // 调用订单详情接口
      const response = await request({
        path: '/api/high-tank/app/order/info',
        method: 'GET',
        query: {
          orderNo: orderNo,
        },
      });

      // console.log('订单详情:', response);

      if (response && response.code === 200 && response.data) {
        setOrderDetail(response.data);
      } else {
        showToast({
          title: response?.message || '获取订单详情失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('订单详情页面加载，订单号:', orderNo);

    // 获取订单详情
    fetchOrderDetail();
  });

  // 打开退款模态框
  const openRefundModal = () => {
    setShowRefundModal(true);

    // 如果refundInfoList有数据，则填充最后一条数据中的applyReason
    if (orderDetail.refundInfoList && orderDetail.refundInfoList.length > 0) {
      const lastRefundInfo = orderDetail.refundInfoList[0];
      setRefundReason(lastRefundInfo.applyReason || '');
    } else {
      setRefundReason('');
    }
  };

  // 关闭退款模态框
  const closeRefundModal = () => {
    setShowRefundModal(false);
    setRefundReason('');
  };

  // 处理退款原因输入
  const handleRefundReasonChange = (e: { detail: { value: string } }) => {
    setRefundReason(e.detail.value);
  };

  // 提交退款申请
  const submitRefundApplication = async () => {
    if (!refundReason.trim()) {
      showToast({
        title: '请填写退款原因',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    try {
      setSubmitting(true);

      // 调用退款申请接口
      const response = await request({
        path: '/api/high-tank/app/order/refund_apply',
        method: 'POST',
        body: {
          orderNo: orderDetail.orderNo,
          applyReason: refundReason,
        },
      });

      if (response && response.code === 200) {
        showToast({
          title: '退款申请提交成功',
          icon: 'none',
          duration: 2000,
        });

        // 关闭模态框
        closeRefundModal();

        // 重新获取订单详情，更新订单状态
        fetchOrderDetail();
      } else {
        showToast({
          title: response?.message || '退款申请提交失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 打开取消订单模态框
  const openCancelModal = () => {
    setShowCancelModal(true);
  };

  // 关闭取消订单模态框
  const closeCancelModal = () => {
    setShowCancelModal(false);
  };

  // 提交取消订单请求
  const submitCancelOrder = async () => {
    if (!orderDetail.orderNo) {
      showToast({
        title: '订单号不存在',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    try {
      setCancelSubmitting(true);

      // 调用取消订单接口
      const response = await request({
        path: '/api/high-tank/app/order/cancel',
        method: 'GET',
        query: {
          orderNo: orderDetail.orderNo,
        },
      });

      if (response && response.code === 200) {
        showToast({
          title: '订单取消成功',
          icon: 'success',
          duration: 2000,
        });

        // 关闭模态框
        closeCancelModal();

        // 重新获取订单详情，更新订单状态
        fetchOrderDetail();
      } else {
        showToast({
          title: response?.message || '订单取消失败',
          icon: 'none',
          duration: 2000,
        });
      }
    } finally {
      setCancelSubmitting(false);
    }
  };

  // 处理线下退款
  const handleOfflineRefund = () => {
    if (!orderDetail.orderNo) {
      showToast({
        title: '订单号不存在',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    // 使用 showModal 显示确认对话框
    showModal({
      title: '确认线下退款',
      content: '确定要标记该订单为已线下退款吗？',
      confirmText: '确认',
      cancelText: '取消',
      success: async res => {
        // 用户点击了确认按钮
        if (res.confirm) {
          // 显示加载提示
          showToast({
            title: '处理中...',
            icon: 'loading',
            duration: 2000,
          });

          // 调用线下退款接口
          const response = await request({
            path: '/api/high-tank/app/order/offlinePay',
            method: 'GET',
            query: {
              orderNo: orderDetail.orderNo,
            },
          });

          // console.log('线下退款接口调用结果:', response);

          if (response && response.code === 200) {
            showToast({
              title: '线下退款成功',
              icon: 'success',
              duration: 2000,
            });

            // 重新获取订单详情，更新订单状态
            fetchOrderDetail();
          }
        }
      },
    });
  };

  return (
    <View className="order-detail-page">
      <NavBar title="订单详情" onBack={() => navigateBack()} />

      <ScrollView className="page-body" scrollY>
        {loading ? (
          <View className="loading">
            <Text className="loading-text">加载中...</Text>
          </View>
        ) : (
          <>
            {/* 退款信息列表卡片 - 当存在退款信息时显示 */}
            {orderDetail.refundInfoList &&
              orderDetail.refundInfoList.length > 0 && (
                <View className="order-card refund-info-card">
                  <View className="card-header">
                    <Text className="card-title">退款信息</Text>
                  </View>

                  <View className="refund-info-list">
                    {processRefundInfoList(orderDetail.refundInfoList).map(
                      (refundInfo, index) => (
                        <View key={index} className="refund-info-item">
                          {/* 退款状态图标和标题 */}
                          <View className="refund-status-header">
                            <View
                              className={`refund-status-icon ${
                                refundInfo.status === '1'
                                  ? 'green'
                                  : refundInfo.status === '2'
                                    ? 'blue'
                                    : refundInfo.status === '2-1'
                                      ? 'green'
                                      : refundInfo.status === '2-2'
                                        ? 'red'
                                        : refundInfo.status === '3'
                                          ? 'orange'
                                          : refundInfo.status === '3-2'
                                            ? 'blue'
                                            : refundInfo.status === '3-3'
                                              ? 'green'
                                              : refundInfo.status === '3-4'
                                                ? 'red'
                                                : 'purple'
                              }`}
                            >
                              {refundInfo.status === '1' ||
                              refundInfo.status === '2-1' ||
                              refundInfo.status === '3' ||
                              refundInfo.status === '3-3' ? (
                                <Text className="status-icon-text">✓</Text>
                              ) : refundInfo.status === '2-2' ||
                                refundInfo.status === '3-4' ? (
                                <Text className="status-icon-text">✕</Text>
                              ) : (
                                <Text className="status-icon-text">
                                  {index + 1}
                                </Text>
                              )}
                            </View>
                            <Text className="refund-status-title">
                              {refundInfo.data.title}
                            </Text>
                          </View>

                          {/* 退款信息详情 */}
                          <View className="refund-info-details">
                            {/* 左侧竖线 */}
                            <View
                              className={`refund-info-line ${
                                index ===
                                processRefundInfoList(
                                  orderDetail.refundInfoList,
                                ).length -
                                  1
                                  ? 'last'
                                  : ''
                              }`}
                            ></View>

                            {/* 详细信息 */}
                            <View className="refund-info-content">
                              {renderRefundItemDetails(refundInfo)}
                            </View>
                          </View>
                        </View>
                      ),
                    )}
                  </View>
                </View>
              )}

            {/* 订单取消信息卡片 - 仅在订单已取消时显示 */}
            {orderDetail.status === 2 && (
              <View className="order-card cancel-info-card">
                <View className="card-header">
                  <Text className="card-title">订单取消信息</Text>
                </View>

                <View className="order-details">
                  <View className="detail-row">
                    <Text className="detail-label">取消方式</Text>
                    <Text className="detail-value">
                      {orderDetail.cancelType === 1 ? '手动取消' : '自动取消'}
                    </Text>
                  </View>
                  <View className="detail-row">
                    <Text className="detail-label">取消时间</Text>
                    <Text className="detail-value">
                      {formatDateTime(orderDetail.cancelTime)}
                    </Text>
                  </View>
                </View>
              </View>
            )}

            {/* 产品使用信息卡片 - 当productType===2并且productUseInfoList存在时显示 */}
            {orderDetail.productType === 2 &&
              orderDetail.productUseInfoList &&
              orderDetail.productUseInfoList.length > 0 && (
                <View className="order-card product-use-info-card">
                  <View className="card-header">
                    <Text className="card-title">产品使用信息</Text>
                  </View>

                  <View className="product-use-info-list">
                    {orderDetail.productUseInfoList.map((useInfo, index) => (
                      <View key={index} className="product-use-info-item">
                        <View className="use-info-details">
                          <View className="detail-row">
                            <Text className="detail-label">核销人姓名</Text>
                            <Text className="detail-value">
                              {useInfo.verifyUserName || '系统管理员'}
                            </Text>
                          </View>

                          <View className="detail-row">
                            <Text className="detail-label">核销人工号</Text>
                            <Text className="detail-value">
                              {useInfo.verifyUserNo || '-'}
                            </Text>
                          </View>

                          <View className="detail-row">
                            <Text className="detail-label">核销时间</Text>
                            <Text className="detail-value">
                              {formatDateTime(useInfo.verifyTime)}
                            </Text>
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              )}

            {/* 产品信息卡片 */}
            <View className="order-card product-card">
              <View className="card-header">
                <Text className="card-title">产品信息</Text>
              </View>

              {/* 订单状态印章 */}
              {orderDetail.status && (
                <View
                  className={`status-stamp ${getStatusColorClass(
                    orderDetail.status,
                  )}`}
                >
                  <Text className="stamp-text">
                    {getStatusText(orderDetail.status)}
                  </Text>
                </View>
              )}

              <View className="order-header">
                <View className="order-title-container">
                  <View
                    className={`order-type-tag ${
                      getOrderTypeText(orderDetail.productType) === '旅客高舱'
                        ? 'blue'
                        : 'cyan'
                    }`}
                  >
                    <Text className="tag-text">
                      {getOrderTypeText(orderDetail.productType)}
                    </Text>
                  </View>
                  <Text className="order-title">
                    {orderDetail.productName || ''}
                  </Text>
                </View>
              </View>

              {/* 根据产品类型显示不同的内容 */}
              {orderDetail.productType === 1 ? (
                // 员工次卡
                <>
                  {/* 优惠券码 */}
                  <View className="coupon-code-section">
                    <Text className="coupon-code-label">优惠券码</Text>
                    {orderDetail.codeNoList &&
                      orderDetail.codeNoList.length > 0 &&
                      orderDetail.codeNoList.map((code, index) => (
                        <Text key={index} className="coupon-code-value">
                          {code}
                        </Text>
                      ))}
                  </View>

                  {/* 产品详情 */}
                  <View className="product-details">
                    <View className="product-detail-item">
                      <Text className="product-detail-label">数量</Text>
                      <Text className="product-detail-value">1</Text>
                    </View>
                    <View className="product-detail-item">
                      <Text className="product-detail-label">使用时间</Text>
                      <Text className="product-detail-value">
                        {orderDetail.effectTime}小时/次
                      </Text>
                    </View>
                    {/* 价格 - 除了待支付和已取消状态外都显示 */}
                    <View className="product-detail-item">
                      <Text className="product-detail-label">价格</Text>
                      <Text className="product-detail-value">
                        ¥ {orderDetail.price || ''}
                      </Text>
                    </View>
                    {/* 剩余次数 */}
                    {orderDetail.times && orderDetail.useTimes && (
                      <View className="product-detail-item">
                        <Text className="product-detail-label">剩余次数</Text>
                        <Text className="product-detail-value">
                          {orderDetail.times - Number(orderDetail.useTimes)}(总
                          {orderDetail.times})
                        </Text>
                      </View>
                    )}
                  </View>
                </>
              ) : (
                // 旅客高舱
                <>
                  {/* 核销码 */}
                  {orderDetail.codeNoList &&
                    orderDetail.codeNoList.length > 0 && (
                      <View className="verification-code-section">
                        <Text className="verification-code-label">核销码</Text>
                        <View className="verification-code-container">
                          {orderDetail.codeNoList[0]
                            .split('')
                            .map((digit, index) => (
                              <Text
                                key={index}
                                className="verification-code-digit"
                              >
                                {digit}
                              </Text>
                            ))}
                        </View>
                      </View>
                    )}

                  {/* 产品详情 */}
                  <View className="product-details">
                    <View className="product-detail-item">
                      <Text className="product-detail-label">数量</Text>
                      <Text className="product-detail-value">1</Text>
                    </View>
                    <View className="product-detail-item">
                      <Text className="product-detail-label">使用时间</Text>
                      <Text className="product-detail-value">
                        {orderDetail.effectTime}小时/次
                      </Text>
                    </View>
                    {/* 价格 - 除了待支付和已取消状态外都显示 */}
                    <View className="product-detail-item">
                      <Text className="product-detail-label">价格</Text>
                      <Text className="product-detail-value">
                        ¥ {orderDetail.price || ''}
                      </Text>
                    </View>

                    {orderDetail.status !== 1 &&
                      orderDetail.status !== 2 &&
                      orderDetail.times &&
                      orderDetail.useTimes && (
                        <View className="product-detail-item">
                          <Text className="product-detail-label">剩余次数</Text>
                          <Text className="product-detail-value">
                            {orderDetail.times - Number(orderDetail.useTimes)}
                            (总
                            {orderDetail.times})
                          </Text>
                        </View>
                      )}
                  </View>
                </>
              )}
            </View>

            {/* 订单信息卡片 */}
            <DetailCard title="订单信息" className="order-info-card">
              <View className="order-details">
                <DetailRow label="订单号" value={orderDetail.orderNo || ''} />
                <DetailRow
                  label="创建人"
                  value={orderDetail.createdName || ''}
                />
                <DetailRow
                  label="创建工号"
                  value={orderDetail.createdNo || ''}
                />
                <DetailRow
                  label="创建日期"
                  value={formatDateTime(orderDetail.createdTime)}
                />
                <DetailRow
                  label="支付日期"
                  value={
                    orderDetail.payTime
                      ? formatDateTime(orderDetail.payTime)
                      : '-'
                  }
                />
                <DetailRow
                  label="支付交易号"
                  value={orderDetail.payOrderNo || '-'}
                />
                <DetailRow
                  label="支付价格"
                  value={
                    orderDetail.status !== 1 && orderDetail.status !== 2
                      ? `¥ ${orderDetail.price || '-'}`
                      : '-'
                  }
                />
              </View>
            </DetailCard>

            {/* 购买人信息卡片 */}
            {(orderDetail.passengerName ||
              orderDetail.phone ||
              orderDetail.fltNo) && (
              <DetailCard title="购买人信息" className="passenger-card">
                <View className="order-details">
                  {orderDetail.passengerName && (
                    <DetailRow
                      label={
                        orderDetail.productType === 1 ? '员工姓名' : '旅客姓名'
                      }
                      value={orderDetail.passengerName}
                    />
                  )}
                  {orderDetail.userNo && (
                    <DetailRow label="购买人工号" value={orderDetail.userNo} />
                  )}
                  {orderDetail.phone && (
                    <DetailRow label="联系方式" value={orderDetail.phone} />
                  )}
                  {orderDetail.fltNo && (
                    <DetailRow label="航班号" value={orderDetail.fltNo} />
                  )}
                  {orderDetail.fltDate && (
                    <DetailRow label="航班日期" value={orderDetail.fltDate} />
                  )}
                  {orderDetail.boardGate && (
                    <DetailRow label="登机口" value={orderDetail.boardGate} />
                  )}
                  {orderDetail.seatNo && (
                    <DetailRow label="座位号" value={orderDetail.seatNo} />
                  )}
                </View>
              </DetailCard>
            )}

            {/* 产品使用信息卡片 - 当productType===1并且productUseInfoList存在时显示 */}
            {orderDetail.productType === 1 &&
              orderDetail.productUseInfoList &&
              orderDetail.productUseInfoList.length > 0 && (
                <View className="order-card product-use-info-card">
                  <View className="card-header">
                    <Text className="card-title">产品使用信息</Text>
                  </View>

                  <View className="product-use-info-list">
                    {orderDetail.productUseInfoList.map((useInfo, index) => (
                      <View key={index} className="product-use-info-item">
                        {/* 用户信息头部 */}
                        <View className="use-info-header">
                          <View className="user-info">
                            <Text className="user-name">
                              {useInfo.passengerName || ''}
                            </Text>
                            <Text className="user-no">
                              {useInfo.userNo || useInfo.phone || ''}
                            </Text>
                          </View>
                          <View
                            className={`user-type-tag ${
                              useInfo.userNo ? 'employee' : 'friend'
                            }`}
                          >
                            <Text className="tag-text">
                              {useInfo.userNo ? '员工' : '亲友'}
                            </Text>
                          </View>
                        </View>

                        {/* 航班信息 */}
                        {(useInfo.fltNo ||
                          useInfo.fltDate ||
                          useInfo.boardGate ||
                          useInfo.seatNo) && (
                          <View className="flight-info-section">
                            <Text className="section-title">航班信息</Text>
                            <View className="flight-info-details">
                              {useInfo.fltNo && (
                                <View className="detail-row">
                                  <Text className="detail-label">航班号</Text>
                                  <Text className="detail-value">
                                    {useInfo.fltNo}
                                  </Text>
                                </View>
                              )}
                              {useInfo.fltDate && (
                                <View className="detail-row">
                                  <Text className="detail-label">航班日期</Text>
                                  <Text className="detail-value">
                                    {useInfo.fltDate}
                                  </Text>
                                </View>
                              )}
                              {useInfo.boardGate && (
                                <View className="detail-row">
                                  <Text className="detail-label">登机口</Text>
                                  <Text className="detail-value">
                                    {useInfo.boardGate}
                                  </Text>
                                </View>
                              )}
                              {useInfo.seatNo && (
                                <View className="detail-row">
                                  <Text className="detail-label">座位号</Text>
                                  <Text className="detail-value">
                                    {useInfo.seatNo}
                                  </Text>
                                </View>
                              )}
                            </View>
                          </View>
                        )}

                        {/* 使用信息 */}
                        <View className="usage-info-section">
                          <Text className="section-title">使用信息</Text>
                          <View className="usage-info-details">
                            <View className="detail-row">
                              <Text className="detail-label">核销人姓名</Text>
                              <Text className="detail-value">
                                {useInfo.verifyUserName || ''}
                              </Text>
                            </View>
                            <View className="detail-row">
                              <Text className="detail-label">核销人工号</Text>
                              <Text className="detail-value">
                                {useInfo.verifyUserNo || ''}
                              </Text>
                            </View>
                            <View className="detail-row">
                              <Text className="detail-label">核销时间</Text>
                              <Text className="detail-value">
                                {formatDateTime(useInfo.verifyTime)}
                              </Text>
                            </View>
                            <View className="detail-row">
                              <Text className="detail-label">使用券号</Text>
                              <Text className="detail-value">
                                {useInfo.codeNo || ''}
                              </Text>
                            </View>
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              )}
          </>
        )}
      </ScrollView>

      {/* 固定在底部的按钮 - 根据订单状态显示不同按钮 */}
      {!loading && (
        <View className="bottom-placeholder">
          {/* 待使用状态显示退款按钮 */}
          {orderDetail.status === 3 && (
            <View className="fixed-bottom-actions">
              <Button
                className="action-button refund-button"
                onClick={openRefundModal}
              >
                退款
              </Button>
            </View>
          )}

          {/* 待支付状态显示取消订单和去支付按钮 */}
          {orderDetail.status === 1 && (
            <View className="fixed-bottom-actions">
              <View className="dual-button-container">
                <Button
                  className="action-button cancel-button"
                  onClick={openCancelModal}
                >
                  取消订单
                </Button>
                <Button
                  className="action-button pay-button"
                  onClick={() => {
                    // 跳转到支付页面，并传递订单号参数
                    navigateTo({
                      url: `/subpackagesHt/pages/payment/index?orderNo=${orderDetail.orderNo}`,
                    });
                  }}
                >
                  去支付
                </Button>
              </View>
            </View>
          )}

          {/* 退款失败状态显示已线下退款按钮 */}
          {orderDetail.status === 8 && (
            <View className="fixed-bottom-actions">
              <Button
                className="action-button offline-refund-button"
                onClick={handleOfflineRefund}
              >
                已线下退款
              </Button>
            </View>
          )}
        </View>
      )}

      {/* 退款原因模态框 */}
      {showRefundModal && (
        <View className="refund-modal-overlay">
          <View className="refund-modal">
            {/* 模态框头部 */}
            <View className="refund-modal-content">
              <View className="refund-modal-header">
                <Text className="refund-modal-title">请填写退款原因</Text>
                <View className="refund-modal-close" onClick={closeRefundModal}>
                  <Text className="close-icon">×</Text>
                </View>
              </View>

              {/* 分割线 */}
              <View className="refund-modal-divider"></View>

              {/* 模态框主体 */}
              <View className="refund-modal-body">
                {/* 退款原因输入 */}
                <View className="refund-form-container">
                  <Text className="refund-form-label">退款原因</Text>
                  <View className="refund-input-container">
                    <Textarea
                      className="refund-reason-input"
                      placeholder="请输入"
                      value={refundReason}
                      onInput={handleRefundReasonChange}
                      maxlength={100}
                    />
                  </View>
                </View>

                {/* 快速填写选项 */}
                <View className="refund-form-container">
                  <Text className="refund-form-label">快速填写</Text>
                  <View className="refund-tag-group">
                    <View
                      className={`refund-tag ${
                        refundReason === '天气原因' ? 'active' : ''
                      }`}
                      onClick={() => setRefundReason(old => old + '天气原因')}
                    >
                      <Text className="refund-tag-text">天气原因</Text>
                    </View>
                    <View
                      className={`refund-tag ${
                        refundReason === '客户不想要了' ? 'active' : ''
                      }`}
                      onClick={() =>
                        setRefundReason(old => old + '客户不想要了')
                      }
                    >
                      <Text className="refund-tag-text">客户不想要了</Text>
                    </View>
                    <View
                      className={`refund-tag ${
                        refundReason === '行程变更' ? 'active' : ''
                      }`}
                      onClick={() => setRefundReason(old => old + '行程变更')}
                    >
                      <Text className="refund-tag-text">行程变更</Text>
                    </View>
                  </View>
                </View>
              </View>
            </View>

            {/* 模态框底部按钮 */}
            <View className="refund-modal-footer">
              <Button className="refund-cancel-btn" onClick={closeRefundModal}>
                取消
              </Button>
              <Button
                className="refund-submit-btn"
                onClick={submitRefundApplication}
                loading={submitting}
                disabled={submitting}
              >
                提交
              </Button>
            </View>
          </View>
        </View>
      )}

      {/* 取消订单确认模态框 */}
      {showCancelModal && (
        <View className="cancel-modal-overlay">
          <View className="cancel-modal">
            {/* 模态框内容 */}
            <View className="cancel-modal-content">
              {/* 警告图标 */}
              <View className="cancel-icon-container">
                <View className="cancel-icon">
                  <Text className="cancel-icon-text">!</Text>
                </View>
              </View>

              {/* 标题 */}
              <Text className="cancel-modal-title">取消订单</Text>

              {/* 确认文本 */}
              <Text className="cancel-modal-text">确认是否取消订单？</Text>

              {/* 按钮区域 */}
              <View className="cancel-modal-buttons">
                <Button className="cancel-btn" onClick={closeCancelModal}>
                  取消
                </Button>
                <Button
                  className="confirm-btn"
                  onClick={submitCancelOrder}
                  loading={cancelSubmitting}
                  disabled={cancelSubmitting}
                >
                  确定
                </Button>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
