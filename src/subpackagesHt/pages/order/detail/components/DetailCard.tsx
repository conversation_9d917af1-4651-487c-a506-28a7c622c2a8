import { View, Text } from '@tarojs/components';
import { ReactNode } from 'react';

interface DetailCardProps {
  title: string;
  children: ReactNode;
  className?: string;
}

export default function DetailCard({ title, children, className = '' }: DetailCardProps) {
  return (
    <View className={`order-card ${className}`}>
      <View className="card-header">
        <Text className="card-title">{title}</Text>
      </View>
      {children}
    </View>
  );
}
