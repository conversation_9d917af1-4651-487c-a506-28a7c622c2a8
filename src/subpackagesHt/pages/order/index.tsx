import {
  View,
  Text,
  Image,
  ScrollView,
  Input,
  Button,
  Picker,
} from '@tarojs/components';
import { useLoad, useRouter, showToast, navigateTo } from '@tarojs/taro';
import { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import {
  getStatusText,
  getStatusColorClass,
  formatDateTime,
} from '@ht/utils/orderUtils';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

// 订单状态类型
type OrderStatus =
  | '全部'
  | '待支付'
  | '已使用'
  | '退款中'
  | '待使用'
  | '使用中'
  | '已退款'
  | '已取消'
  | '退款失败';

// 订单类型
type OrderType = '旅客高舱' | '员工次卡';

// API返回的订单列表项接口
interface OrderListVO {
  productId?: number;
  productName?: string;
  productType?: number; // 产品类型，1：员工次卡；2：旅客高舱
  price?: string;
  status?: number; // 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用
  createdName?: string;
  createdTime?: string;
  orderNo?: string; // 订单号
}

// 订单查询参数接口
interface OrderQueryVo {
  createdName?: string; // 创建人
  createdStartTime?: string; // 创建开始日期
  createdEndTime?: string; // 创建结束日期
  orderTypeList?: number[]; // 订单类型，1：员工次卡；2：旅客高舱
  statusList?: number[]; // 状态，1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用；8：退款失败
}

// 筛选选项类型
interface FilterOptions {
  orderTypeList: string[]; // 产品类型：['全部'] | ['员工次卡', '旅客高舱'] 等
  // API参数: 1：员工次卡；2：旅客高舱
  statusList: string[]; // 订单状态：['全部'] | ['待支付', '已取消', '待使用'] 等
  // API参数: 1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用；8：退款失败
  createdStartTime: string; // 创建开始日期
  createdEndTime: string; // 创建结束日期
}

// 前端展示的订单数据类型
interface OrderItem {
  id: string;
  type: OrderType;
  title: string;
  status: OrderStatus;
  creator: string;
  createTime: string;
  price: string;
  orderNo: string; // 订单号，用于跳转到详情页
}

export default function OrderPage() {
  const router = useRouter();
  const { type = 'my' } = router.params; // 只获取type参数

  // 页面类型：type=my表示我的销售订单，type=all表示全部销售信息
  const isMyOrders = type === 'my';

  // 页面标题
  const pageTitle = isMyOrders ? '我的销售订单' : '全部销售订单';

  // 用户名参数用于筛选"我的销售订单"
  // 移除 activeStatus，使用 filterOptions.statusList 统一管理状态

  // 搜索关键字
  const [searchKeyword, setSearchKeyword] = useState('');

  // 状态标签列表
  const statusTabs: OrderStatus[] = [
    '全部',
    '待支付',
    '已使用',
    '退款中',
    '待使用',
    '使用中',
    '已退款',
    '已取消',
    '退款失败',
  ];

  // 订单数据状态
  const [orderList, setOrderList] = useState<OrderItem[]>([]);

  // 加载状态
  const [loading, setLoading] = useState(false);

  // 下拉刷新状态
  const [refreshing, setRefreshing] = useState(false);

  // 筛选弹窗显示状态
  const [showFilterModal, setShowFilterModal] = useState(false);

  // 获取今天的日期，格式为YYYY-MM-DD
  const today = dayjs().format('YYYY-MM-DD');
  // 获取七天前的日期
  const sevenDaysAgo = dayjs().subtract(7, 'day').format('YYYY-MM-DD');

  // 筛选选项状态
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    orderTypeList: ['全部'],
    statusList: ['全部'],
    createdStartTime: sevenDaysAgo,
    createdEndTime: today,
  });

  // 临时筛选选项，用于在弹窗中选择但未确认时
  const [tempFilterOptions, setTempFilterOptions] = useState<FilterOptions>({
    orderTypeList: ['全部'],
    statusList: ['全部'],
    createdStartTime: sevenDaysAgo,
    createdEndTime: today,
  });

  // 获取订单列表
  const fetchOrderList = async () => {
    // 构建查询参数
    const queryParams: OrderQueryVo = {};

    // 添加筛选条件 - 产品类型
    if (
      !filterOptions.orderTypeList.includes('全部') &&
      filterOptions.orderTypeList.length > 0
    ) {
      const orderTypeListCodes: number[] = [];

      // 将选中的产品类型转换为对应的状态码
      filterOptions.orderTypeList.forEach(productType => {
        if (productType === '员工次卡') {
          orderTypeListCodes.push(1); // 1：员工次卡
        } else if (productType === '旅客高舱') {
          orderTypeListCodes.push(2); // 2：旅客高舱
        }
      });

      if (orderTypeListCodes.length > 0) {
        queryParams.orderTypeList = orderTypeListCodes;
      }
    }

    // 添加筛选条件 - 订单状态
    if (
      !filterOptions.statusList.includes('全部') &&
      filterOptions.statusList.length > 0
    ) {
      // 根据状态文本获取对应的状态码
      // 1：待支付；2：已取消；3：待使用；4：退款中；5：已退款；6：使用中；7：已使用；8：退款失败
      const statusCodes: number[] = [];

      // 将选中的订单状态转换为对应的状态码
      filterOptions.statusList.forEach(status => {
        const statusCode = getStatusCode(status as OrderStatus);
        if (statusCode) {
          statusCodes.push(statusCode);
        }
      });

      if (statusCodes.length > 0) {
        queryParams.statusList = statusCodes;
      }
    }

    // 添加筛选条件 - 创建时间范围
    if (filterOptions.createdStartTime) {
      queryParams.createdStartTime = filterOptions.createdStartTime;
    }
    if (filterOptions.createdEndTime) {
      queryParams.createdEndTime = filterOptions.createdEndTime;
    }

    // 如果是全部销售信息页面且有搜索关键字，添加创建人筛选条件
    if (!isMyOrders && searchKeyword.trim()) {
      queryParams.createdName = searchKeyword.trim();
    }

    // 使用构建好的查询参数获取订单列表
    await fetchOrderListWithParams(queryParams);
  };

  // 使用指定参数获取订单列表
  const fetchOrderListWithParams = async (queryParams: OrderQueryVo) => {
    try {
      setLoading(true);

      // console.log('查询参数:', queryParams);

      // 根据页面类型调用不同的订单列表接口
      const apiPath = isMyOrders
        ? '/api/high-tank/app/order/list/my' // 我的销售订单接口
        : '/api/high-tank/app/order/list'; // 全部销售信息接口

      // console.log('调用接口路径:', apiPath);

      const response = await request({
        path: apiPath,
        method: 'POST',
        body: queryParams,
      });

      // console.log('订单列表响应:', response);

      if (response && response.code === 200 && response.data) {
        // 将API返回的数据转换为前端展示的格式
        const orders: OrderItem[] = response.data.map(
          (item: OrderListVO, index: number) => {
            return {
              id: (index + 1).toString(),
              type: item.productType === 1 ? '员工次卡' : '旅客高舱',
              title: item.productName || '',
              status: getStatusText(item.status),
              creator: item.createdName || '',
              createTime: formatDateTime(item.createdTime),
              price: item.price || '',
              orderNo: item.orderNo || '', // 添加订单号
            };
          },
        );

        setOrderList(orders);
      } else {
        showToast({
          title: response?.message || '获取订单列表失败',
          icon: 'none',
          duration: 2000,
        });
        setOrderList([]);
      }
    } catch (error) {
      setOrderList([]);
    } finally {
      setLoading(false);
    }
  };

  // 将状态文本转换为状态码
  const getStatusCode = (status: OrderStatus): number | null => {
    switch (status) {
      case '待支付':
        return 1; // 1：待支付
      case '已取消':
        return 2; // 2：已取消
      case '待使用':
        return 3; // 3：待使用
      case '退款中':
        return 4; // 4：退款中
      case '已退款':
        return 5; // 5：已退款
      case '使用中':
        return 6; // 6：使用中
      case '已使用':
        return 7; // 7：已使用
      case '退款失败':
        return 8; // 8：退款失败
      default:
        return null;
    }
  };

  // 监听筛选条件变化，自动重新获取订单列表
  useEffect(() => {
    fetchOrderList();
  }, [filterOptions]);

  useLoad(() => {
    // console.log(`${pageTitle}页面加载，类型:`, type);
    // console.log('是否我的销售订单:', isMyOrders);
    // 初始化时会通过 useEffect 自动获取订单列表
  });

  // 处理状态标签点击
  const handleStatusTabClick = (status: OrderStatus) => {
    // 更新筛选条件中的状态列表
    setFilterOptions(prev => ({
      ...prev,
      statusList: [status],
    }));
  };

  // 处理搜索输入
  const handleSearchInput = (e: { detail: { value: string } }) => {
    setSearchKeyword(e.detail.value);
  };

  // 处理搜索确认
  const handleSearchConfirm = () => {
    // console.log('搜索确认，关键字:', searchKeyword);

    // 重新获取订单列表
    fetchOrderList();
  };

  // 处理筛选点击
  const handleFilterClick = () => {
    // console.log('筛选点击');
    // 打开筛选弹窗时，将当前筛选条件复制到临时筛选条件
    setTempFilterOptions({
      orderTypeList: filterOptions.orderTypeList,
      statusList: filterOptions.statusList,
      createdStartTime: filterOptions.createdStartTime,
      createdEndTime: filterOptions.createdEndTime,
    });
    setShowFilterModal(true);
  };

  // 处理筛选选项变更
  const handleFilterOptionChange = (
    optionType:
      | 'orderTypeList'
      | 'statusList'
      | 'createdStartTime'
      | 'createdEndTime',
    value: string,
  ) => {
    if (optionType === 'createdStartTime' || optionType === 'createdEndTime') {
      // 日期选择器，直接设置值
      setTempFilterOptions(prev => ({
        ...prev,
        [optionType]: value,
      }));
    } else {
      // 多选处理
      setTempFilterOptions(prev => {
        // 如果选择了"全部"
        if (value === '全部') {
          return {
            ...prev,
            [optionType]: ['全部'],
          };
        }

        // 当前选项列表
        const currentList = [...prev[optionType]];

        // 如果当前列表中有"全部"，需要移除
        const filteredList = currentList.filter(item => item !== '全部');

        // 检查值是否已经在列表中
        const valueIndex = filteredList.indexOf(value);

        if (valueIndex === -1) {
          // 值不在列表中，添加
          filteredList.push(value);
        } else {
          // 值已在列表中，移除
          filteredList.splice(valueIndex, 1);
        }

        // 如果列表为空，则默认选择"全部"
        if (filteredList.length === 0) {
          return {
            ...prev,
            [optionType]: ['全部'],
          };
        }

        return {
          ...prev,
          [optionType]: filteredList,
        };
      });
    }
  };

  // 处理筛选确认
  const handleFilterConfirm = () => {
    // 更新筛选条件，useEffect 会自动重新获取订单列表
    setFilterOptions(tempFilterOptions);
    // 关闭弹窗
    setShowFilterModal(false);
  };

  // 处理下拉刷新
  const handleRefresh = async () => {
    // console.log('下拉刷新');
    setRefreshing(true);

    try {
      // 重新获取订单列表
      await fetchOrderList();
    } catch (error) {
      // console.error('下拉刷新失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // 处理订单点击，跳转到订单详情页
  const handleOrderClick = (order: OrderItem) => {
    if (!order.orderNo) {
      showToast({
        title: '订单号不存在',
        icon: 'none',
        duration: 2000,
      });
      return;
    }

    // console.log('跳转到订单详情页，订单号:', order.orderNo);

    // 跳转到订单详情页
    navigateTo({
      url: `/subpackagesHt/pages/order/detail/index?orderNo=${order.orderNo}`,
    });
  };

  return (
    <View className="all-orders-page">
      {/* 顶部导航栏 */}
      <NavBar title={pageTitle} showBack />

      {/* 页面内容 */}
      <View className="page-body">
        {/* 搜索栏 - 仅在全部销售信息页面显示 */}
        {!isMyOrders && (
          <View className="search-bar">
            <Image
              className="search-icon"
              src={getImageUrl('search-2-line.svg')}
            />
            <Input
              className="search-input"
              placeholder="请输入创建人"
              placeholderClass="placeholder"
              value={searchKeyword}
              onInput={handleSearchInput}
              onConfirm={handleSearchConfirm}
              confirmType="search"
            />
          </View>
        )}

        {/* 状态标签栏 */}
        <View className="status-bar">
          <ScrollView className="status-tabs" scrollX>
            {statusTabs.map(status => {
              // 判断当前状态是否被选中
              const isActive = filterOptions.statusList.includes(status);
              // 是否选中了"全部"
              const isAllSelected = filterOptions.statusList.includes('全部');

              // 显示逻辑：
              // 1. 如果选中了"全部"，只高亮"全部"标签
              // 2. 如果选中了具体状态，高亮这些状态标签
              const shouldHighlight = isAllSelected
                ? status === '全部'
                : isActive;

              return (
                <View
                  key={status}
                  className={`status-tab ${shouldHighlight ? 'active' : ''}`}
                  onClick={() => handleStatusTabClick(status)}
                >
                  <Text className="tab-text">{status}</Text>
                </View>
              );
            })}
          </ScrollView>
          <View className="status-divider"></View>
          <View className="filter-button" onClick={handleFilterClick}>
            <Image
              className="filter-icon"
              src={getImageUrl('filter-3-fill.svg')}
            />
            <Text className="filter-text">
              筛选
              {(() => {
                // 计算选中的具体状态数量（排除"全部"）
                const selectedSpecificCount = filterOptions.statusList.filter(
                  s => s !== '全部',
                ).length;
                const isAllSelected = filterOptions.statusList.includes('全部');

                // 如果选中了多个具体状态，显示数量
                if (!isAllSelected && selectedSpecificCount > 1) {
                  return `(${selectedSpecificCount})`;
                }
                return '';
              })()}
            </Text>
          </View>
        </View>

        {/* 订单列表 */}
        <ScrollView
          className="order-list"
          scrollY
          refresherEnabled
          refresherTriggered={refreshing}
          onRefresherRefresh={handleRefresh}
        >
          {loading ? (
            <View className="loading">
              <Text className="loading-text">加载中...</Text>
            </View>
          ) : orderList.length > 0 ? (
            orderList.map((order: OrderItem) => (
              <View
                key={order.id}
                className="order-card"
                onClick={() => handleOrderClick(order)}
              >
                {/* 订单头部 */}
                <View className="order-header">
                  <View className="order-title-container">
                    <View
                      className={`order-type-tag ${
                        order.type === '旅客高舱' ? 'blue' : 'cyan'
                      }`}
                    >
                      <Text className="tag-text">{order.type}</Text>
                    </View>
                    <Text className="order-title">{order.title}</Text>
                  </View>
                  <View className="order-status">
                    <Text
                      className={`status-text ${getStatusColorClass(
                        getStatusCode(order.status) || undefined,
                      )}`}
                    >
                      {order.status}
                    </Text>
                  </View>
                </View>

                {/* 分割线 */}
                <View className="order-divider"></View>

                {/* 订单详情 */}
                <View className="order-details">
                  <View className="detail-row">
                    <Text className="detail-label">创建人</Text>
                    <Text className="detail-value">{order.creator}</Text>
                  </View>
                  <View className="detail-row">
                    <Text className="detail-label">创建时间</Text>
                    <Text className="detail-value">{order.createTime}</Text>
                  </View>
                  <View className="detail-row">
                    <Text className="detail-label">产品价格</Text>
                    <Text className="detail-value">¥ {order.price}</Text>
                  </View>
                </View>
              </View>
            ))
          ) : (
            <View className="empty-list">
              <Text className="empty-text">暂无订单数据</Text>
            </View>
          )}
        </ScrollView>
      </View>

      {/* 筛选弹窗 */}
      {showFilterModal && (
        <View
          className="filter-modal-overlay"
          onClick={() => setShowFilterModal(false)}
        >
          <View className="filter-modal" onClick={e => e.stopPropagation()}>
            {/* 弹窗内容 */}
            <View className="filter-modal-content">
              {/* 标题 */}
              <View className="filter-modal-header">
                <Text className="filter-modal-title">筛选</Text>
                <View
                  className="filter-modal-close"
                  onClick={() => setShowFilterModal(false)}
                >
                  <Image
                    className="close-icon"
                    src={getImageUrl('close-circle-fill.svg')}
                  />
                </View>
              </View>

              {/* 分割线 */}
              <View className="filter-modal-divider"></View>

              {/* 创建时间筛选 */}
              <View className="filter-section">
                <Text className="filter-section-title">创建日期:</Text>
                <View className="date-picker-container">
                  <View className="date-picker-item">
                    <Picker
                      mode="date"
                      value={tempFilterOptions.createdStartTime}
                      onChange={e =>
                        handleFilterOptionChange(
                          'createdStartTime',
                          e.detail.value,
                        )
                      }
                    >
                      <View className="date-picker-value">
                        <Text className="date-text">
                          {tempFilterOptions.createdStartTime || '开始日期'}
                        </Text>
                        <Image
                          className="date-icon"
                          src={getImageUrl('calendar.svg')}
                        />
                      </View>
                    </Picker>
                  </View>
                  <Text className="date-separator">~</Text>
                  <View className="date-picker-item">
                    <Picker
                      mode="date"
                      value={tempFilterOptions.createdEndTime}
                      onChange={e =>
                        handleFilterOptionChange(
                          'createdEndTime',
                          e.detail.value,
                        )
                      }
                    >
                      <View className="date-picker-value">
                        <Text className="date-text">
                          {tempFilterOptions.createdEndTime || '结束日期'}
                        </Text>
                        <Image
                          className="date-icon"
                          src={getImageUrl('calendar.svg')}
                        />
                      </View>
                    </Picker>
                  </View>
                </View>
              </View>

              {/* 产品类型筛选 */}
              <View className="filter-section">
                <Text className="filter-section-title">产品类型:</Text>
                <View className="filter-options">
                  <View
                    className={`filter-option ${
                      tempFilterOptions.orderTypeList.includes('全部')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('orderTypeList', '全部')
                    }
                  >
                    <Text className="filter-option-text">全部</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.orderTypeList.includes('员工次卡')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('orderTypeList', '员工次卡')
                    }
                  >
                    <Text className="filter-option-text">员工次卡</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.orderTypeList.includes('旅客高舱')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('orderTypeList', '旅客高舱')
                    }
                  >
                    <Text className="filter-option-text">旅客高舱</Text>
                  </View>
                </View>
              </View>

              {/* 订单状态筛选 */}
              <View className="filter-section">
                <Text className="filter-section-title">订单状态:</Text>
                <View className="filter-options">
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('全部')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '全部')
                    }
                  >
                    <Text className="filter-option-text">全部</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('待支付')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '待支付')
                    }
                  >
                    <Text className="filter-option-text">待支付</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('已使用')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '已使用')
                    }
                  >
                    <Text className="filter-option-text">已使用</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('退款中')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '退款中')
                    }
                  >
                    <Text className="filter-option-text">退款中</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('待使用')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '待使用')
                    }
                  >
                    <Text className="filter-option-text">待使用</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('使用中')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '使用中')
                    }
                  >
                    <Text className="filter-option-text">使用中</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('已退款')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '已退款')
                    }
                  >
                    <Text className="filter-option-text">已退款</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('已取消')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '已取消')
                    }
                  >
                    <Text className="filter-option-text">已取消</Text>
                  </View>
                  <View
                    className={`filter-option ${
                      tempFilterOptions.statusList.includes('退款失败')
                        ? 'active'
                        : ''
                    }`}
                    onClick={() =>
                      handleFilterOptionChange('statusList', '退款失败')
                    }
                  >
                    <Text className="filter-option-text">退款失败</Text>
                  </View>
                </View>
              </View>
            </View>

            {/* 底部按钮 */}
            <View className="filter-modal-footer">
              <Button
                className="filter-cancel-btn"
                onClick={() => setShowFilterModal(false)}
              >
                取消
              </Button>
              <Button
                className="filter-confirm-btn"
                onClick={handleFilterConfirm}
              >
                筛选
              </Button>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
