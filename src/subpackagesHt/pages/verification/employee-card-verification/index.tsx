import { View, Text, Image, Button, Input, Picker } from '@tarojs/components';
import Taro, {
  useLoad,
  useRouter,
  scanCode,
  reLaunch,
  showToast,
} from '@tarojs/taro';
import { useState, useCallback } from 'react';
import dayjs from 'dayjs';
import NavBar from '@ht/components/NavBar';
import request from '@ht/api/apiConfig';
import { getScanResults } from '@ht/utils/scanParsing';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import './index.less';

// 步骤枚举
enum VerificationStep {
  AddEmployee = 1,
  AddRelative = 2,
  Confirmation = 3,
  Completed = 4,
}

export default function EmployeeCardVerification() {
  const router = useRouter();
  const { jobNumber } = router.params;

  // 员工信息
  const [employeeInfo, setEmployeeInfo] = useState({
    passengerName: '',
    userNo: jobNumber || '',
    phone: '',
  });

  // 产品信息列表
  const [productList, setProductList] = useState<
    Array<{
      productName: string;
      remainingCount: string;
      totalCount: string;
      purchaseDate: string;
      id: string;
    }>
  >([]);

  // 优惠券信息
  const [couponInfo, setCouponInfo] = useState({
    codeNo: '',
    orderNo: '',
    productName: '',
    isVisible: false,
  });

  // 当前步骤
  const [currentStep, setCurrentStep] = useState<VerificationStep>(
    VerificationStep.AddEmployee,
  );

  // 亲友信息数组
  const [relatives, setRelatives] = useState<
    Array<{
      id: string;
      passengerName: string; // 旅客姓名
      phone: string; // 手机号
      fltNo: string; // 航班号
      fltDate: string; // 航班日期
      boardGate: string; // 登机口
      seatNo: string; // 座位号
      codeNo: string; // 券码编号
      couponVisible: boolean;
      orderNo?: string; // 订单号
      productName?: string;
      userNo?: string; // 工号
    }>
  >([]);

  // 日期选择器范围
  const [dateRange] = useState({
    start: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
    end: dayjs().add(30, 'day').format('YYYY-MM-DD'),
  });

  // 当前编辑的亲友索引
  const [currentRelativeIndex, setCurrentRelativeIndex] = useState(-1);

  // 加载状态
  const [loading, setLoading] = useState(true);

  // 获取产品信息
  const fetchProductInfo = async () => {
    if (!jobNumber) {
      setLoading(false);
      return;
    }

    // 使用userNo作为API参数

    try {
      // 直接获取产品信息
      const response = await request({
        path: '/api/high-tank/app/productCode/productOrderInfo',
        method: 'GET',
        query: { userNo: jobNumber },
      });

      // console.log('获取产品信息成功:', response);

      if (response && response.data) {
        // 更新员工信息
        setEmployeeInfo({
          passengerName: response.data.passengerName || '',
          userNo: response.data.jobNumber || jobNumber,
          phone: response.data.phone || '',
        });

        // 处理产品列表数据
        if (
          response.data.productOrderList &&
          Array.isArray(response.data.productOrderList)
        ) {
          const productOrderList = response.data.productOrderList;

          // 更新产品列表
          const formattedProductList = productOrderList.map((item: any) => ({
            productName: item.productName || '',
            remainingCount: item.times - item.useTimes || '',
            totalCount: item.times || '',
            purchaseDate: item.createdTime || '',
            id: item.id || '',
          }));

          setProductList(formattedProductList);

          // 产品列表为空时的处理
          if (formattedProductList.length === 0) {
            // console.log('没有可用产品');
          }
        }
      }
    } catch (error) {
      // console.error('获取产品信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    // console.log('员工次卡核销页面加载, jobNumber:', jobNumber);
    fetchProductInfo();
  });

  // 获取当前步骤名称
  const getCurrentStepName = (): string => {
    // 根据当前步骤返回对应的名称
    switch (currentStep) {
      case VerificationStep.AddEmployee:
        return '添加员工';
      case VerificationStep.AddRelative:
        return '添加亲友';
      case VerificationStep.Confirmation:
        return '核销确认';
      case VerificationStep.Completed:
        return '核销完成';
      default:
        return '添加员工';
    }
  };

  // 添加新亲友
  const addNewRelative = useCallback(() => {
    // 创建新亲友对象
    const newRelative = {
      id: Date.now().toString(), // 使用时间戳作为临时ID
      passengerName: '',
      phone: '',
      fltNo: '',
      fltDate: '',
      boardGate: '',
      seatNo: '',
      codeNo: '',
      couponVisible: false,
      userNo: '',
      productName: '',
    };

    // 添加到亲友数组
    setRelatives(prev => [...prev, newRelative]);

    // 设置当前编辑的亲友索引为新添加的亲友
    setCurrentRelativeIndex(relatives.length);
  }, [relatives.length]);

  // 删除亲友
  const deleteRelative = useCallback(
    (index: number) => {
      setRelatives(prev => prev.filter((_, i) => i !== index));

      // 如果删除后没有亲友了，重置当前编辑的亲友索引
      if (relatives.length <= 1) {
        setCurrentRelativeIndex(-1);
      } else if (index === currentRelativeIndex) {
        // 如果删除的是当前编辑的亲友，重置当前编辑的亲友索引
        setCurrentRelativeIndex(0);
      }
    },
    [relatives.length, currentRelativeIndex],
  );

  // 更新亲友信息
  const updateRelative = useCallback(
    (index: number, field: string, value: any) => {
      setRelatives(prev =>
        prev.map((relative, i) =>
          i === index ? { ...relative, [field]: value } : relative,
        ),
      );
    },
    [],
  );

  // 定义登机牌信息类型
  interface BoardingInfo {
    passengerName?: string;
    phone?: string;
    fltNo?: string;
    fltDate?: string;
    boardGate?: string;
    seatNo?: string;
    userNo?: string;
    [key: string]: any; // 允许其他属性
  }

  // 验证扫码结果是否包含必要信息
  const isValidBoardingInfo = (info: BoardingInfo | null): boolean => {
    return !!(info && (info.fltNo || info.fltDate || info.seatNo));
  };

  // 将扫码结果转换为登机牌信息
  const convertScanResultToBoardingInfo = (
    scanResult: any,
  ): BoardingInfo | null => {
    if (!scanResult) {
      return null;
    }

    return {
      passengerName: scanResult.paxName || '',
      fltNo: scanResult.flightNo || '',
      fltDate: scanResult.flightDate || '',
      boardGate: scanResult.gate || scanResult.orgCityAirport || '',
      seatNo: scanResult.seatNo || '',
      userNo: scanResult.userNo || '',
    };
  };

  // 扫描登机牌
  const handleScanBoardingPass = (index: number) => {
    // 调用扫码API
    scanCode({
      onlyFromCamera: false, // 允许从相册选择二维码
      scanType: ['qrCode', 'barCode'], // 扫码类型，支持二维码和条形码
      success: res => {
        try {
          // 获取扫码结果
          const scanResult = res.result;
          // console.log('扫码结果:', scanResult);

          // 使用工具函数解析扫码结果
          const scanResultInfo = getScanResults(scanResult);
          // console.log('解析后的登机牌信息:', scanResultInfo);

          // 转换为登机牌信息格式
          const boardingInfo = convertScanResultToBoardingInfo(scanResultInfo);

          // 验证登机牌信息
          if (isValidBoardingInfo(boardingInfo)) {
            const info = boardingInfo as BoardingInfo; // 类型断言，避免null检查

            // 更新亲友的航班信息
            if (info.fltNo) {
              updateRelative(index, 'fltNo', info.fltNo);
            }
            if (info.fltDate) {
              updateRelative(index, 'fltDate', info.fltDate);
            }
            if (info.boardGate) {
              updateRelative(index, 'boardGate', info.boardGate);
            }
            if (info.seatNo) {
              updateRelative(index, 'seatNo', info.seatNo);
            }

            // 如果有旅客姓名，也可以更新
            if (info.passengerName) {
              updateRelative(index, 'passengerName', info.passengerName);
            }

            // 如果有工号，也可以更新
            if (info.userNo) {
              updateRelative(index, 'userNo', info.userNo);
            }
          } else {
            // console.warn('扫码结果缺少必要信息:', scanResultInfo);

            showToast({
              title: '登机牌信息不完整，请手动填写',
              icon: 'none',
              duration: 2000,
            });
          }
        } catch (error) {
          // console.error('处理扫码结果失败:', error);

          showToast({
            title: '解析登机牌信息失败，请手动填写',
            icon: 'none',
            duration: 2000,
          });
        }
      },
      // fail: error => {
      fail: () => {
        // console.error('扫码失败:', error);
        showToast({
          title: '扫码失败',
          icon: 'none',
          duration: 2000,
        });
      },
    });
  };

  // 处理步骤变化
  const handleStepChange = useCallback(
    (step: VerificationStep) => {
      setCurrentStep(step);
      // console.log(`切换到步骤: ${step}`);

      // 当切换到步骤2时，重置亲友相关状态
      if (step === VerificationStep.AddRelative && relatives.length === 0) {
        // 如果没有亲友，可以在这里添加其他初始化逻辑
      }

      // 当进入核销完成步骤时的逻辑
      if (step === VerificationStep.Completed) {
        // console.log('核销完成');
      }
    },
    [relatives.length],
  );

  // 渲染底部按钮
  const renderBottomButtons = () => {
    switch (currentStep) {
      case VerificationStep.AddEmployee:
        return (
          <Button
            className={`next-button ${!couponInfo.isVisible ? 'disabled' : ''}`}
            onClick={() => {
              if (couponInfo.isVisible) {
                // 更新步骤为"2-添加亲友"
                handleStepChange(VerificationStep.AddRelative);
              } else {
                showToast({
                  title: '请先扫描员工优惠券',
                  icon: 'none',
                  duration: 2000,
                });
              }
            }}
          >
            下一步
          </Button>
        );

      case VerificationStep.AddRelative: {
        return (
          <View className="button-group">
            <Button
              className="prev-button"
              onClick={() => handleStepChange(VerificationStep.AddEmployee)}
            >
              上一步
            </Button>
            <Button
              className="next-button"
              onClick={() => {
                // 检查是否有亲友，以及亲友信息是否完整
                // 如果没有添加亲友，则允许直接进入下一步
                // 如果添加了亲友，则需要验证所有亲友信息是否完整
                const hasRelatives = relatives.length > 0;

                // 验证手机号格式的函数
                const isValidPhoneNumber = (phone: string): boolean => {
                  // 中国大陆手机号格式：1开头的11位数字
                  const phoneRegex = /^1[3-9]\d{9}$/;
                  return phoneRegex.test(phone);
                };

                // 检查所有亲友信息是否完整和有效
                const validateRelatives = (): {
                  isValid: boolean;
                  message: string;
                } => {
                  if (!hasRelatives) {
                    return { isValid: true, message: '' };
                  }

                  // 验证旅客姓名格式（不允许输入数字，支持英文、汉字与分隔符[.]）
                  const isValidPassengerName = (name: string): boolean => {
                    // 匹配英文字母、汉字和点号，不允许数字
                    const nameRegex = /^[a-zA-Z\u4e00-\u9fa5.]+$/;
                    return nameRegex.test(name);
                  };

                  for (let i = 0; i < relatives.length; i++) {
                    const relative = relatives[i];
                    const relativeIndex = i + 1;

                    if (!relative.passengerName) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请填写旅客姓名`,
                      };
                    }

                    // 验证旅客姓名格式
                    if (!isValidPassengerName(relative.passengerName)) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：旅客姓名不允许输入数字，仅支持英文、汉字与分隔符[.]`,
                      };
                    }

                    if (!relative.phone) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请填写手机号码`,
                      };
                    }

                    if (!isValidPhoneNumber(relative.phone)) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：手机号格式不正确`,
                      };
                    }

                    if (!relative.fltNo) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请填写航班号`,
                      };
                    }

                    // 验证航班号长度（最长6位）
                    if (relative.fltNo.length > 6) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：航班号长度不能超过6位`,
                      };
                    }

                    if (!relative.fltDate) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请选择航班日期`,
                      };
                    }

                    if (!relative.boardGate) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请填写登机口`,
                      };
                    }

                    // 验证登机口长度（最长6位）
                    if (relative.boardGate.length > 6) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：登机口长度不能超过6位`,
                      };
                    }

                    if (!relative.seatNo) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请填写座位号`,
                      };
                    }

                    // 验证座位号长度（最长5位）
                    if (relative.seatNo.length > 5) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：座位号长度不能超过5位`,
                      };
                    }

                    if (!relative.codeNo || !relative.couponVisible) {
                      return {
                        isValid: false,
                        message: `亲友${relativeIndex}：请扫描次卡优惠券`,
                      };
                    }
                  }

                  return { isValid: true, message: '' };
                };

                // 只有在点击下一步按钮时才执行验证
                if (!hasRelatives) {
                  // 如果没有添加亲友，直接进入下一步
                  handleStepChange(VerificationStep.Confirmation);
                } else {
                  // 有亲友时，验证亲友信息
                  const validationResult = validateRelatives();
                  if (validationResult.isValid) {
                    handleStepChange(VerificationStep.Confirmation);
                  } else {
                    showToast({
                      title: validationResult.message,
                      icon: 'none',
                      duration: 2000,
                    });
                  }
                }
              }}
            >
              下一步
            </Button>
          </View>
        );
      }

      case VerificationStep.Confirmation:
        return (
          <View className="button-group">
            <Button
              className="prev-button"
              onClick={() => handleStepChange(VerificationStep.AddRelative)}
            >
              上一步
            </Button>
            <Button
              className="submit-button"
              onClick={async () => {
                // 提交核销
                // console.log('提交核销参数 - 员工信息:', employeeInfo);
                // console.log('提交核销参数 - 员工优惠券:', couponInfo);
                // console.log('提交核销参数 - 亲友信息:', relatives);
                // console.log('提交核销参数 - 产品列表:', productList);

                // 构建核销参数对象，按照API要求的格式
                // 定义VerifyCouponInfoVo类型
                interface VerifyCouponInfoVo {
                  codeNo: string;
                  orderNo: string;
                  passengerName: string;
                  phone: string;
                  userNo: string;
                  fltNo?: string;
                  fltDate?: string;
                  boardGate?: string;
                  seatNo?: string;
                }

                const verifyCouponInfoList: VerifyCouponInfoVo[] = [];

                // 添加员工券码信息
                verifyCouponInfoList.push({
                  codeNo: couponInfo.codeNo,
                  orderNo: couponInfo.orderNo,
                  passengerName: employeeInfo.passengerName,
                  phone: employeeInfo.phone,
                  userNo: employeeInfo.userNo,
                  // 以下字段可选，如果有则添加
                  fltNo: '',
                  fltDate: '',
                  boardGate: '',
                  seatNo: '',
                });

                // 添加亲友券码信息
                relatives.forEach(relative => {
                  if (relative.codeNo && relative.couponVisible) {
                    verifyCouponInfoList.push({
                      codeNo: relative.codeNo,
                      orderNo: relative.orderNo || '',
                      passengerName: relative.passengerName,
                      phone: relative.phone,
                      // userNo: relative.userNo || '',
                      userNo: '',
                      fltNo: relative.fltNo,
                      fltDate: relative.fltDate,
                      boardGate: relative.boardGate,
                      seatNo: relative.seatNo,
                    });
                  }
                });

                // 构建API请求参数
                const verificationParams = {
                  verifyCouponInfoVoList: verifyCouponInfoList,
                };

                // console.log('提交核销完整参数:', verificationParams);

                // 调用核销接口
                try {
                  const response = await request({
                    path: '/api/high-tank/app/productCode/verifyCoupon',
                    method: 'POST',
                    body: verificationParams,
                    isloading: true,
                    errorIsToast: false,
                  });

                  // console.log('核销结果:', response);

                  if (response && response.code === 200) {
                    // 继续核销流程
                    handleStepChange(VerificationStep.Completed);
                  } else {
                    // 显示API返回的错误信息
                    Taro.showModal({
                      title: response?.message || '核销失败，请稍后重试',
                      showCancel: false,
                    });
                  }
                } catch (error) {
                  // console.error('核销接口调用失败:', error);
                  // 显示自定义错误信息

                  Taro.showModal({
                    title: error?.message || '网络异常，请检查网络连接后重试',
                    showCancel: false,
                  });
                }
              }}
            >
              提交核销
            </Button>
          </View>
        );

      case VerificationStep.Completed:
        return (
          <Button
            className="next-button"
            onClick={() => {
              // 返回核销首页
              // console.log('返回核销首页');
              reLaunch({
                url: '/subpackagesHt/pages/verification/index',
              });
            }}
          >
            返回核销首页
          </Button>
        );

      default:
        return <Button className="next-button">返回首页</Button>;
    }
  };

  // 渲染步骤内容
  function renderStepContent() {
    switch (currentStep) {
      case VerificationStep.AddEmployee:
        return renderEmployeeStep();
      case VerificationStep.AddRelative:
        return renderRelativeStep();
      case VerificationStep.Confirmation:
        return renderConfirmationStep();
      case VerificationStep.Completed:
        return renderCompletedStep();
      default:
        return null;
    }
  }

  // 渲染步骤1：添加员工
  function renderEmployeeStep() {
    return (
      <>
        {/* 步骤1: 员工信息卡片 */}
        <View className="info-card">
          <Text className="card-title">员工信息</Text>

          <View className="info-row">
            <Text className="info-label">员工姓名</Text>
            <Text className="info-value">
              {employeeInfo.passengerName || '未知'}
            </Text>
          </View>

          <View className="info-row">
            <Text className="info-label">工号</Text>
            <Text className="info-value">{employeeInfo.userNo || '未知'}</Text>
          </View>

          <View className="info-row">
            <Text className="info-label">联系电话</Text>
            <Text className="info-value">{employeeInfo.phone || '未知'}</Text>
          </View>
        </View>

        {/* 可用产品卡片 */}
        <View className="info-card">
          <Text className="card-title">可用产品</Text>

          {productList.map((product, index) => (
            <View key={product.id || index} className="product-card">
              <View className="product-image">
                <View className="product-image-v">V</View>
              </View>
              <View className="product-content">
                <Text className="product-name">员工次卡</Text>
                <View className="product-details">
                  <Text className="product-detail">
                    剩余次数:{product.remainingCount || '0'}(总
                    {product.totalCount || '0'})
                  </Text>
                  <Text className="product-detail">
                    购买日期:
                    {product.purchaseDate
                      ? dayjs(product.purchaseDate).format('YYYY-MM-DD')
                      : '未知'}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>

        {/* 员工优惠券 */}
        <View className="info-card">
          <Text className="card-title">员工优惠券</Text>

          {!couponInfo.isVisible ? (
            <View
              className="coupon-scan-card"
              onClick={() => {
                scanCode({
                  scanType: ['qrCode', 'barCode'],
                  success: async res => {
                    // console.log('扫码结果:', res.result);

                    // 扫码结果是券码，调用接口获取优惠券详情
                    const codeNo = res.result;

                    // 调用检查优惠券接口
                    const response = await request({
                      path: '/api/high-tank/app/productCode/checkCoupon',
                      method: 'GET',
                      query: { codeNo },
                      isloading: true,
                    });

                    // console.log('获取优惠券详情成功:', response);

                    if (response && response.data) {
                      // 更新优惠券信息
                      setCouponInfo({
                        codeNo: codeNo,
                        orderNo: response.data.orderNo || '',
                        productName: response.data.productName || '',
                        isVisible: true,
                      });
                    }
                  },
                  // fail: err => {
                  fail: () => {
                    // console.error('扫码失败:', err);
                    showToast({
                      title: '扫码失败',
                      icon: 'error',
                      duration: 2000,
                    });
                  },
                });
              }}
            >
              <Image
                className="coupon-scan-icon"
                src={getImageUrl('qr_scan_icon.svg')}
                mode="aspectFit"
              />
              <Text className="coupon-scan-text">扫描次卡优惠券</Text>
            </View>
          ) : (
            <View className="coupon-info-container">
              <View className="coupon-info-header">
                <View className="coupon-info-row">
                  <Text className="coupon-info-label">优惠券码</Text>
                  <Text className="coupon-info-value">{couponInfo.codeNo}</Text>
                </View>
                <View
                  className="coupon-refresh-btn"
                  onClick={() => {
                    setCouponInfo({ ...couponInfo, isVisible: false });
                  }}
                >
                  <Image
                    className="refresh-icon"
                    src={getImageUrl('refresh_icon.svg')}
                    mode="aspectFit"
                  />
                  <Text className="refresh-text">重新扫码</Text>
                </View>
              </View>

              <View className="coupon-info-row">
                <Text className="coupon-info-label">关联产品订单</Text>
                <Text className="coupon-info-value">{couponInfo.orderNo}</Text>
              </View>

              <View className="coupon-info-row">
                <Text className="coupon-info-label">关联产品</Text>
                <View>
                  <View className="coupon-product-tag">
                    <Text className="coupon-product-text">员工次卡</Text>
                  </View>
                  <Text>{couponInfo.productName}</Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </>
    );
  }

  // 渲染步骤2：添加亲友
  function renderRelativeStep() {
    return (
      <>
        {/* 步骤2: 添加亲友 */}
        <View className="info-card">
          <View className="add-relative-header">
            <Text className="card-title">添加亲友</Text>
            <View className="add-btn" onClick={addNewRelative}>
              <Image
                className="add-icon"
                src={getImageUrl('add_icon.svg')}
                mode="aspectFit"
              />
              <Text className="add-text">添加</Text>
            </View>
          </View>
        </View>

        {/* 亲友列表 */}
        {relatives.map((relative, index) => (
          <View className="info-card" key={relative.id}>
            <View className="relative-card-header">
              <View className="relative-tag-container">
                <View className="relative-tag">
                  <Text className="relative-tag-text">{index + 1}</Text>
                </View>
                <Text className="relative-title">亲友</Text>
              </View>
              <Image
                className="delete-icon"
                src={getImageUrl('delete_icon.svg')}
                mode="aspectFit"
                onClick={() => deleteRelative(index)}
              />
            </View>

            <View className="divider"></View>

            {/* 航班信息 */}
            <View className="section-header">
              <Text className="section-title">航班信息</Text>
              <View
                className="scan-btn"
                onClick={() => handleScanBoardingPass(index)}
              >
                <Image
                  className="scan-icon"
                  src={getImageUrl('qr_scan_icon.svg')}
                  mode="aspectFit"
                />
                <Text className="scan-text">扫登机牌快速录入</Text>
              </View>
            </View>

            {/* 航班号 */}
            <View className="form-item">
              <Text className="form-label">航班号</Text>
              <Input
                className="form-input"
                type="text"
                value={relative.fltNo || ''}
                placeholder="请输入航班号"
                maxlength={6}
                onInput={e => updateRelative(index, 'fltNo', e.detail.value)}
              />
            </View>

            {/* 航班日期 */}
            <View className="form-item">
              <Text className="form-label">航班日期</Text>
              <Picker
                mode="date"
                value={relative.fltDate || dayjs().format('YYYY-MM-DD')}
                start={dateRange.start}
                end={dateRange.end}
                onChange={e => updateRelative(index, 'fltDate', e.detail.value)}
              >
                <View className="picker-view">
                  <Input
                    className="form-input date-input"
                    placeholder="请选择"
                    value={relative.fltDate}
                    disabled
                  />
                  <Image
                    className="calendar-icon"
                    src={getImageUrl('calendar.svg')}
                    mode="aspectFit"
                  />
                </View>
              </Picker>
            </View>

            {/* 登机口 */}
            <View className="form-item">
              <Text className="form-label">登机口</Text>
              <Input
                className="form-input"
                type="text"
                value={relative.boardGate || ''}
                placeholder="请输入登机口"
                maxlength={6}
                onInput={e =>
                  updateRelative(index, 'boardGate', e.detail.value)
                }
              />
            </View>

            {/* 座位号 */}
            <View className="form-item">
              <Text className="form-label">座位号</Text>
              <Input
                className="form-input"
                type="text"
                value={relative.seatNo || ''}
                placeholder="请输入座位号"
                maxlength={5}
                onInput={e => updateRelative(index, 'seatNo', e.detail.value)}
              />
            </View>

            <View className="divider"></View>

            {/* 旅客信息 */}
            <Text className="section-title">旅客信息</Text>

            {/* 旅客姓名 */}
            <View className="form-item">
              <Text className="form-label">旅客姓名</Text>
              <Input
                className="form-input"
                type="text"
                value={relative.passengerName || ''}
                placeholder="请输入旅客姓名"
                onInput={e =>
                  updateRelative(index, 'passengerName', e.detail.value)
                }
              />
            </View>

            {/* 手机号码 */}
            <View className="form-item">
              <Text className="form-label">手机号码</Text>
              <Input
                className="form-input"
                type="number"
                value={relative.phone || ''}
                maxlength={11}
                placeholder="请输入手机号码"
                onInput={e => updateRelative(index, 'phone', e.detail.value)}
              />
            </View>

            <View className="divider"></View>

            {/* 亲友优惠券 */}
            <Text className="section-title">亲友优惠券</Text>

            {!relative.couponVisible ? (
              <View
                className="coupon-scan-card"
                onClick={() => {
                  scanCode({
                    scanType: ['qrCode', 'barCode'],
                    success: async res => {
                      // console.log('亲友优惠券扫码结果:', res.result);

                      // 扫码结果是券码，调用接口获取优惠券详情
                      const codeNo = res.result;

                      // 显示加载提示
                      showToast({
                        title: '获取优惠券信息...',
                        icon: 'loading',
                        duration: 2000,
                      });

                      // 调用检查优惠券接口
                      const response = await request({
                        path: '/api/high-tank/app/productCode/checkCoupon',
                        method: 'GET',
                        query: { codeNo },
                      });

                      // console.log('获取亲友优惠券详情成功:', response);

                      if (response && response.data) {
                        // 更新亲友优惠券信息
                        updateRelative(index, 'codeNo', codeNo);
                        updateRelative(index, 'couponVisible', true);
                        // 如果有订单号和产品名称，也更新
                        if (response.data.orderNo) {
                          updateRelative(
                            index,
                            'orderNo',
                            response.data.orderNo,
                          );
                        }
                        // 设置固定的产品名称
                        updateRelative(
                          index,
                          'productName',
                          response.data.productName,
                        );

                        showToast({
                          title: '获取优惠券成功',
                          icon: 'success',
                          duration: 2000,
                        });
                      } else {
                        showToast({
                          title: '无效的优惠券',
                          icon: 'error',
                          duration: 2000,
                        });
                      }
                    },
                    // fail: err => {
                    fail: () => {
                      // console.error('扫码失败:', err);
                      showToast({
                        title: '扫码失败',
                        icon: 'error',
                        duration: 2000,
                      });
                    },
                  });
                }}
              >
                <Image
                  className="coupon-scan-icon"
                  src={getImageUrl('qr_scan_icon.svg')}
                  mode="aspectFit"
                />
                <Text className="coupon-scan-text">扫描次卡优惠券</Text>
              </View>
            ) : (
              <View className="coupon-info-container">
                <View className="coupon-info-header">
                  <View className="coupon-info-row">
                    <Text className="coupon-info-label">优惠券码</Text>
                    <Text className="coupon-info-value">{relative.codeNo}</Text>
                  </View>
                  <View
                    className="coupon-refresh-btn"
                    onClick={() =>
                      updateRelative(index, 'couponVisible', false)
                    }
                  >
                    <Image
                      className="refresh-icon"
                      src={getImageUrl('refresh_icon.svg')}
                      mode="aspectFit"
                    />
                    <Text className="refresh-text">重新扫码</Text>
                  </View>
                </View>

                <View className="coupon-info-row">
                  <Text className="coupon-info-label">关联产品订单</Text>
                  <Text className="coupon-info-value">
                    {relative.orderNo || '-'}
                  </Text>
                </View>

                <View className="coupon-info-row">
                  <Text className="coupon-info-label">关联产品</Text>
                  <View>
                    <View className="coupon-product-tag">
                      <Text className="coupon-product-text">员工次卡</Text>
                    </View>
                    <Text>{relative.productName}</Text>
                  </View>
                </View>
              </View>
            )}
          </View>
        ))}
      </>
    );
  }

  // 渲染步骤3：核销确认
  function renderConfirmationStep() {
    return (
      <>
        {/* 步骤3: 核销确认 */}
        {/* 员工信息卡片 */}
        <View className="info-card">
          <Text className="card-title">员工信息</Text>

          <View className="info-row">
            <Text className="info-label">员工姓名</Text>
            <Text className="info-value">
              {employeeInfo.passengerName || '未知'}
            </Text>
          </View>

          <View className="info-row">
            <Text className="info-label">工号</Text>
            <Text className="info-value">{employeeInfo.userNo || '未知'}</Text>
          </View>

          <View className="info-row">
            <Text className="info-label">联系电话</Text>
            <Text className="info-value">{employeeInfo.phone || '未知'}</Text>
          </View>
        </View>

        {/* 员工优惠券卡片 */}
        <View className="info-card">
          <Text className="card-title">员工优惠券</Text>

          <View className="info-row">
            <Text className="info-label">优惠券码</Text>
            <Text className="info-value">{couponInfo.codeNo}</Text>
          </View>

          <View className="info-row">
            <Text className="info-label">关联产品订单</Text>
            <Text className="info-value">{couponInfo.orderNo}</Text>
          </View>

          <View className="info-row">
            <Text className="info-label">关联产品</Text>
            <View>
              <View className="coupon-product-tag">
                <Text className="coupon-product-text">员工次卡</Text>
              </View>
              <Text>{couponInfo.productName}</Text>
            </View>
          </View>
        </View>

        {/* 亲友信息卡片 */}
        <View className="info-card">
          <Text className="card-title">亲友信息</Text>

          <View className="divider"></View>

          {relatives.length > 0 ? (
            relatives.map((relative, index) => (
              <View className="relative-info-card" key={relative.id}>
                <View className="relative-header">
                  <View className="relative-tag-container">
                    <View className="relative-tag">
                      <Text className="relative-tag-text">{index + 1}</Text>
                    </View>
                    <Text className="relative-name">
                      {relative.passengerName || '未填写'}
                    </Text>
                  </View>
                  <Text className="relative-phone">
                    {relative.phone || '未填写'}
                  </Text>
                </View>

                <Text className="section-title">航班信息</Text>

                <View className="info-row">
                  <Text className="info-label">航班号</Text>
                  <Text className="info-value">
                    {relative.fltNo || '未填写'}
                  </Text>
                </View>

                <View className="info-row">
                  <Text className="info-label">航班日期</Text>
                  <Text className="info-value">
                    {relative.fltDate || '未填写'}
                  </Text>
                </View>

                <View className="info-row">
                  <Text className="info-label">登机口</Text>
                  <Text className="info-value">
                    {relative.boardGate || '未填写'}
                  </Text>
                </View>

                <View className="info-row">
                  <Text className="info-label">座位号</Text>
                  <Text className="info-value">
                    {relative.seatNo || '未填写'}
                  </Text>
                </View>

                <Text className="section-title">亲友优惠券</Text>

                <View className="info-row">
                  <Text className="info-label">优惠券码</Text>
                  <Text className="info-value">
                    {relative.codeNo || '未填写'}
                  </Text>
                </View>

                <View className="info-row">
                  <Text className="info-label">关联产品订单</Text>
                  <Text className="info-value">
                    {relative.orderNo || couponInfo.orderNo || '未填写'}
                  </Text>
                </View>

                <View className="info-row">
                  <Text className="info-label">关联产品</Text>
                  <View>
                    <View className="coupon-product-tag">
                      <Text className="coupon-product-text">员工次卡</Text>
                    </View>
                    <Text>{relative.productName}</Text>
                  </View>
                </View>
              </View>
            ))
          ) : (
            <View className="empty-relatives">
              <Text className="empty-text">暂无亲友信息</Text>
            </View>
          )}
        </View>
      </>
    );
  }

  // 渲染步骤4：核销完成
  function renderCompletedStep() {
    // 计算核销的优惠券总数（员工优惠券 + 亲友优惠券）
    const totalCoupons = 1 + relatives.filter(r => r.couponVisible).length;

    return (
      <>
        {/* 步骤4: 核销完成 */}
        <View className="success-container">
          <Image
            className="success-icon"
            src={getImageUrl('success_icon.svg')}
            mode="aspectFit"
          />
          <View className="success-text-container">
            <Text className="success-title">核销成功</Text>
            <Text className="success-subtitle">
              已核销{totalCoupons}张优惠券
            </Text>
          </View>

          {/* 显示核销详情 */}
          {/* <View className="verification-details">
            <Text className="details-title">核销详情</Text>

            <View className="details-item">
              <Text className="details-label">员工工号:</Text>
              <Text className="details-value">{employeeInfo.userNo}</Text>
            </View>

            <View className="details-item">
              <Text className="details-label">员工姓名:</Text>
              <Text className="details-value">
                {employeeInfo.passengerName}
              </Text>
            </View>

            <View className="details-item">
              <Text className="details-label">员工优惠券:</Text>
              <Text className="details-value">{couponInfo.codeNo}</Text>
            </View>

            <View className="details-item">
              <Text className="details-label">亲友数量:</Text>
              <Text className="details-value">{relatives.length}人</Text>
            </View>

            <View className="details-item">
              <Text className="details-label">核销时间:</Text>
              <Text className="details-value">
                {dayjs().format('YYYY-MM-DD HH:mm:ss')}
              </Text>
            </View>
          </View> */}
        </View>
      </>
    );
  }

  return (
    <View className="employee-card-verification-page">
      <NavBar title={getCurrentStepName()} showBack />

      {loading ? (
        <View className="loading-container">
          <Text className="loading-text">加载中...</Text>
        </View>
      ) : (
        <View className="page-body">
          {/* 步骤指示器 */}
          <View className="steps-container">
            <View
              className={`step ${
                currentStep === VerificationStep.AddEmployee ? 'active' : ''
              }`}
            >
              <View className="step-number">1</View>
              <Text className="step-text">添加员工</Text>
            </View>
            <View className="step-divider"></View>
            <View
              className={`step ${
                currentStep === VerificationStep.AddRelative ? 'active' : ''
              }`}
            >
              <View className="step-number">2</View>
              <Text className="step-text">添加亲友</Text>
            </View>
            <View className="step-divider"></View>
            <View
              className={`step ${
                currentStep === VerificationStep.Confirmation ? 'active' : ''
              }`}
            >
              <View className="step-number">3</View>
              <Text className="step-text">核销确认</Text>
            </View>
            <View className="step-divider"></View>
            <View
              className={`step ${
                currentStep === VerificationStep.Completed ? 'active' : ''
              }`}
            >
              <View className="step-number">4</View>
              <Text className="step-text">核销完成</Text>
            </View>
          </View>

          {/* 根据当前步骤渲染不同的内容 */}
          {renderStepContent()}
        </View>
      )}

      {/* 底部按钮 */}
      <View className="bottom-bar">{renderBottomButtons()}</View>
    </View>
  );
}
