.coupon-card-wrapper-warp {
  width: 100%;
  margin-bottom: 12px;
}

/* 优惠券卡片样式 */
.coupon-card-wrapper {
  width: 100%;
  position: relative;
}

.coupon-card {
  display: flex;
  align-items: center;
  gap: 32px;
  padding: 11px 16px;
  height: 92px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
  z-index: 1;
}

/* 未使用状态的卡片添加点击效果 */
.coupon-card:not(.disabled):active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

// .coupon-card::before {
//   content: '';
//   position: absolute;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
//   background-color: #ebf5f7;
//   z-index: -2;
//   box-shadow: inset 0px 0px 10px rgba(255, 255, 255, 1);
// }

/* 待使用状态下的背景图片 - Image 标签样式 */
.coupon-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  object-fit: cover;
  pointer-events: none; /* 确保图片不会阻止点击事件 */
}

// .coupon-card::after {
//   content: '';
//   position: absolute;
//   top: 0;
//   right: 0;
//   bottom: 0;
//   width: 80px;
//   background: repeating-linear-gradient(
//     -45deg,
//     transparent,
//     transparent 4px,
//     #07798f 4px,
//     #07798f 8px
//   );
//   opacity: 0.3;
//   z-index: -1;
// }

/* 已使用/已失效状态样式 */
.coupon-card.disabled {
  opacity: 0.6;
  background: #fff;
}

/* 状态标记样式 */
.status-mark {
  position: absolute;
  top: 0;
  right: 0;
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transform: translate(32px, -32px);
  z-index: 10;
}

.status-mark.used {
  background-color: rgba(211, 211, 211, 0.8);
  border: 1px solid rgba(169, 169, 169, 0.8);
}

.status-mark.expired {
  background-color: rgba(211, 211, 211, 0.8);
  border: 1px solid rgba(169, 169, 169, 0.8);
}

.status-text {
  font-size: 14px;
  color: #666;
  font-family: 'MiSans', sans-serif;
  white-space: nowrap;
  position: absolute;
  top: 50%;
  right: 36px;
  transform: translate(50%, -50%);
}

/* 左侧样式 */
.coupon-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.coupon-type {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.57em;
  font-family: 'MiSans', sans-serif;
}

.coupon-type,
.coupon-desc {
  color: #0750df;
}

.coupon-type,
.coupon-desc {
  color: #206597;
}

.coupon-desc {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.67em;
  font-family: 'MiSans', sans-serif;
}

/* 右侧样式 */
.coupon-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
}

.coupon-title-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.coupon-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.57em;
  color: #23242d;
  font-family: 'MiSans', sans-serif;
}

.coupon-code-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.code-label {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.67em;
  color: #4f5170;
  font-family: 'MiSans', sans-serif;
}

.coupon-code {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.67em;
  color: #4f5170;
  font-family: 'MiSans', sans-serif;
}

.verification-code {
  display: flex;
  align-items: center;
  gap: 4px;
}

.code-digit {
  width: 24px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #9aa2ca;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.67em;
  color: #0052d9;
  font-family: 'MiSans', sans-serif;
}

.instructions-toggle {
  display: flex;
  align-items: center;
  gap: 2px;
  cursor: pointer;
}

.toggle-text {
  font-size: 10px;
  font-weight: 400;
  line-height: 1.8em;
  color: #4f5170;
  font-family: 'MiSans', sans-serif;
}

.arrow-icon {
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8 10.6667L3.33333 6L4.33333 5L8 8.66667L11.6667 5L12.6667 6L8 10.6667Z' fill='%239AA2CA'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s ease;
}

.arrow-icon.expanded {
  transform: rotate(180deg);
}

/* 使用说明样式 */
.instructions-container {
  padding: 16px 16px 8px;
  border-radius: 0 0 12px 12px;
  margin-top: -6px;
  background: #fff;
}

.instructions-title {
  font-size: 12px;
  font-weight: 500;
  line-height: 1.67em;
  color: #23242d;
  font-family: 'MiSans', sans-serif;
  margin-bottom: 4px;
}

.instructions-content {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.67em;
  color: #737578;
  font-family: 'MiSans', sans-serif;
  white-space: pre-wrap;
}
