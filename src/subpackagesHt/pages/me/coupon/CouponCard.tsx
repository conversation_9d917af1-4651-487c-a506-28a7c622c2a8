import { View, Text, Image, ScrollView } from '@tarojs/components';
import { navigateTo } from '@tarojs/taro';
import { getImageUrl } from '@/subpackagesHt/utils/image';
import { Coupon, CouponStatus } from './types';
import './CouponCard.less';

interface CouponCardProps {
  coupon: Coupon;
  onToggleExpand: (codeNo: string) => void;
}

/**
 * 优惠券卡片组件
 */
export default function CouponCard({
  coupon,
  onToggleExpand,
}: CouponCardProps) {
  // 处理展开/收起说明
  const handleToggleInstructions = e => {
    // 阻止事件冒泡，防止触发卡片点击事件
    e.stopPropagation();
    onToggleExpand(coupon.codeNo);
  };

  // 处理卡片点击
  const handleCardClick = () => {
    // 只有未使用的优惠券才能跳转到条码页面
    if (coupon.status === CouponStatus.UNUSED) {
      // 跳转到条码页面
      navigateTo({
        url: `/subpackagesHt/pages/me/coupon/barcode/index?code=${coupon.codeNo}&type=${coupon.type}`,
      });
    }
  };

  // 获取状态标记文本
  const getStatusText = () => {
    switch (coupon.status) {
      case CouponStatus.USED:
        return '已使用';
      case CouponStatus.EXPIRED:
        return '已失效';
      case CouponStatus.UNAVAILABLE:
        return '不可用';
      default:
        return '';
    }
  };

  return (
    <View className="coupon-card-wrapper-warp">
      <View className="coupon-card-wrapper">
        {/* 背景图片 - 仅在待使用状态下显示 */}
        {coupon.status === CouponStatus.UNUSED && (
          <Image
            className="coupon-bg-image"
            src={getImageUrl('coupon_bg.svg')}
            mode="aspectFill"
          />
        )}

        {/* 优惠券卡片 */}
        <View
          className={`coupon-card ${
            coupon.status === CouponStatus.UNUSED ? 'unused' : 'disabled'
          }`}
          onClick={handleCardClick}
        >
          {/* 左侧 */}
          <View className="coupon-left">
            <Text className="coupon-type">员工次卡</Text>
            <Text className="coupon-desc">{coupon.description}</Text>
          </View>
          {/* 右侧 */}
          <View className="coupon-right">
            <View className="coupon-title-container">
              <ScrollView className="coupon-title" scrollX>
                {coupon.name}
              </ScrollView>

              {/* 券码 */}
              <View className="coupon-code-container">
                <Text className="code-label">券码</Text>
                <Text className="coupon-code">{coupon.codeNo}</Text>
              </View>
            </View>

            {/* 使用说明 */}
            <View
              className="instructions-toggle"
              onClick={handleToggleInstructions}
            >
              <Text className="toggle-text">使用说明</Text>
              <View
                className={`arrow-icon ${coupon.isExpanded ? 'expanded' : ''}`}
              />
            </View>

            <Text className="status-text">{getStatusText()}</Text>
          </View>
        </View>
      </View>

      {/* 展开的使用说明 */}
      {coupon.isExpanded && (
        <View className="instructions-container">
          <Text className="instructions-title">使用说明</Text>
          <Text className="instructions-content">{coupon.discribe}</Text>
        </View>
      )}
    </View>
  );
}
